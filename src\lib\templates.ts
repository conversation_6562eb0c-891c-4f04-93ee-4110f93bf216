import { promises as fs } from 'fs';
import path from 'path';

export interface Template {
  id: string;
  name: string;
  description: string;
  filename: string;
  placeholders: string[];
  layoutSize: 'A4' | 'Letter';
  uploadedAt: string;
}

const TEMPLATES_DIR = path.join(process.cwd(), 'public', 'templates');
const TEMPLATES_JSON_PATH = path.join(TEMPLATES_DIR, 'templates.json');

// Ensure templates directory exists
export async function ensureTemplatesDir() {
  try {
    await fs.access(TEMPLATES_DIR);
  } catch {
    await fs.mkdir(TEMPLATES_DIR, { recursive: true });
  }
}

// Load all templates from templates.json
export async function loadTemplates(): Promise<Template[]> {
  try {
    await ensureTemplatesDir();
    const data = await fs.readFile(TEMPLATES_JSON_PATH, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error loading templates:', error);
    return [];
  }
}

// Save templates to templates.json
export async function saveTemplates(templates: Template[]): Promise<void> {
  await ensureTemplatesDir();
  await fs.writeFile(TEMPLATES_JSON_PATH, JSON.stringify(templates, null, 2));
}

// Add a new template
export async function addTemplate(template: Omit<Template, 'uploadedAt'>): Promise<void> {
  const templates = await loadTemplates();
  const newTemplate: Template = {
    ...template,
    uploadedAt: new Date().toISOString(),
  };
  templates.push(newTemplate);
  await saveTemplates(templates);
}

// Update an existing template
export async function updateTemplate(id: string, updates: Partial<Template>): Promise<void> {
  const templates = await loadTemplates();
  const index = templates.findIndex(t => t.id === id);
  if (index !== -1) {
    templates[index] = { ...templates[index], ...updates };
    await saveTemplates(templates);
  }
}

// Delete a template
export async function deleteTemplate(id: string): Promise<void> {
  const templates = await loadTemplates();
  const filteredTemplates = templates.filter(t => t.id !== id);
  await saveTemplates(filteredTemplates);
  
  // Also delete the HTML file
  const template = templates.find(t => t.id === id);
  if (template) {
    try {
      await fs.unlink(path.join(TEMPLATES_DIR, template.filename));
    } catch (error) {
      console.error('Error deleting template file:', error);
    }
  }
}

// Extract placeholders from HTML content
export function extractPlaceholders(htmlContent: string): string[] {
  const placeholderRegex = /\[([^\]]+)\]/g;
  const placeholders = new Set<string>();
  let match;

  while ((match = placeholderRegex.exec(htmlContent)) !== null) {
    placeholders.add(match[0]); // Include the brackets
  }

  return Array.from(placeholders).sort();
}

// Clean HTML content from Word-specific tags and attributes
export function cleanWordHtml(htmlContent: string): string {
  let cleaned = htmlContent;

  // Remove Word-specific XML namespaces and tags
  cleaned = cleaned.replace(/<\/?o:[^>]*>/g, '');
  cleaned = cleaned.replace(/<\/?w:[^>]*>/g, '');
  cleaned = cleaned.replace(/<\/?v:[^>]*>/g, '');

  // Remove Word-specific meta tags and generator info
  cleaned = cleaned.replace(/<meta[^>]*name="Generator"[^>]*>/gi, '');
  cleaned = cleaned.replace(/<meta[^>]*name="ProgId"[^>]*>/gi, '');
  cleaned = cleaned.replace(/<meta[^>]*content="Microsoft Word[^>]*>/gi, '');

  // Remove Word-specific style definitions and font faces
  cleaned = cleaned.replace(/<style>[\s\S]*?\/\* Font Definitions \*\/[\s\S]*?<\/style>/gi, '');
  cleaned = cleaned.replace(/<style>[\s\S]*?@font-face[\s\S]*?<\/style>/gi, '');

  // Remove Word section divs but keep content
  cleaned = cleaned.replace(/<div class=WordSection1>/gi, '<div>');

  // Remove mso- classes and Word-specific classes
  cleaned = cleaned.replace(/class="[^"]*mso[^"]*[^"]*"/gi, '');
  cleaned = cleaned.replace(/class="[^"]*MsoNormal[^"]*"/gi, '');
  cleaned = cleaned.replace(/class="[^"]*MsoNoSpacing[^"]*"/gi, '');
  cleaned = cleaned.replace(/class="[^"]*MsoChpDefault[^"]*"/gi, '');
  cleaned = cleaned.replace(/class="[^"]*MsoPapDefault[^"]*"/gi, '');

  // Remove Word-specific style attributes
  cleaned = cleaned.replace(/style='[^']*word-wrap:break-word[^']*'/gi, '');
  cleaned = cleaned.replace(/style='[^']*text-justify:inter-ideograph[^']*'/gi, 'style="text-align: justify"');
  cleaned = cleaned.replace(/lang=EN-US/gi, '');

  // Clean up margin and spacing styles
  cleaned = cleaned.replace(/margin-top:0in;?\s*/gi, '');
  cleaned = cleaned.replace(/margin-right:0in;?\s*/gi, '');
  cleaned = cleaned.replace(/margin-bottom:10\.0pt;?\s*/gi, '');
  cleaned = cleaned.replace(/margin-left:0in;?\s*/gi, '');
  cleaned = cleaned.replace(/line-height:115%;?\s*/gi, '');

  // Remove empty class and style attributes
  cleaned = cleaned.replace(/\s*class=""\s*/g, ' ');
  cleaned = cleaned.replace(/\s*style=""\s*/g, ' ');
  cleaned = cleaned.replace(/\s*style=''\s*/g, ' ');

  // Remove Word-specific characters and symbols
  cleaned = cleaned.replace(/�+/g, ' ');
  cleaned = cleaned.replace(/&nbsp;/g, ' ');

  // Remove empty spans and divs
  cleaned = cleaned.replace(/<span[^>]*>\s*<\/span>/g, '');
  cleaned = cleaned.replace(/<div[^>]*>\s*<\/div>/g, '');

  // Add proper line breaks and formatting
  cleaned = cleaned.replace(/><p/g, '>\n<p');
  cleaned = cleaned.replace(/<\/p></g, '</p>\n<');
  cleaned = cleaned.replace(/><div/g, '>\n<div');
  cleaned = cleaned.replace(/<\/div></g, '</div>\n<');
  cleaned = cleaned.replace(/><head/g, '>\n<head');
  cleaned = cleaned.replace(/<\/head></g, '</head>\n<');
  cleaned = cleaned.replace(/><body/g, '>\n<body');
  cleaned = cleaned.replace(/<\/body></g, '</body>\n<');

  // Clean up multiple whitespaces but preserve line breaks
  cleaned = cleaned.replace(/[ \t]+/g, ' ');
  cleaned = cleaned.replace(/\n\s*\n/g, '\n');

  // Add proper DOCTYPE and structure if missing
  if (!cleaned.includes('<!DOCTYPE')) {
    cleaned = '<!DOCTYPE html>\n' + cleaned;
  }

  // Add basic CSS for better formatting
  const basicStyles = `
<style>
  body {
    font-family: 'Times New Roman', serif;
    font-size: 12pt;
    line-height: 1.5;
    margin: 1in;
    color: black;
  }
  p {
    margin: 0 0 10pt 0;
  }
  .center {
    text-align: center;
  }
  .justify {
    text-align: justify;
  }
  .bold {
    font-weight: bold;
  }
  .underline {
    text-decoration: underline;
  }
</style>`;

  // Insert basic styles after head tag
  if (cleaned.includes('<head>')) {
    cleaned = cleaned.replace('<head>', '<head>\n' + basicStyles);
  }

  return cleaned.trim();
}

// Generate a unique filename for a template
export function generateTemplateFilename(name: string): string {
  const slug = name
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/^-+|-+$/g, '');
  
  const timestamp = Date.now();
  return `${slug}-${timestamp}.html`;
}

// Save HTML content to a template file
export async function saveTemplateFile(filename: string, htmlContent: string): Promise<void> {
  await ensureTemplatesDir();
  const filePath = path.join(TEMPLATES_DIR, filename);
  await fs.writeFile(filePath, htmlContent, 'utf-8');
}

// Load HTML content from a template file
export async function loadTemplateFile(filename: string): Promise<string> {
  const filePath = path.join(TEMPLATES_DIR, filename);
  return await fs.readFile(filePath, 'utf-8');
}

// Reformat and clean an existing template file
export async function reformatTemplateFile(filename: string): Promise<void> {
  const filePath = path.join(TEMPLATES_DIR, filename);
  const originalContent = await fs.readFile(filePath, 'utf-8');
  const cleanedContent = cleanWordHtml(originalContent);
  await fs.writeFile(filePath, cleanedContent, 'utf-8');
}

// Replace placeholders in HTML content with actual values
export function replacePlaceholders(htmlContent: string, data: Record<string, string>): string {
  let processedContent = htmlContent;

  // Replace each placeholder with its corresponding value
  Object.entries(data).forEach(([key, value]) => {
    const placeholder = `[${key}]`;
    const regex = new RegExp(placeholder.replace(/[[\]]/g, '\\$&'), 'g');
    processedContent = processedContent.replace(regex, value || '');
  });

  return processedContent;
}

// Generate PDF from HTML content with specified layout size
export function generatePDFStyles(layoutSize: 'A4' | 'Letter'): string {
  const pageStyles = layoutSize === 'A4'
    ? {
        width: '210mm',
        height: '297mm',
        margin: '20mm'
      }
    : {
        width: '8.5in',
        height: '11in',
        margin: '1in'
      };

  return `
    <style>
      @page {
        size: ${layoutSize};
        margin: ${pageStyles.margin};
      }

      @media print {
        body {
          width: ${pageStyles.width};
          height: ${pageStyles.height};
          margin: 0;
          padding: ${pageStyles.margin};
          font-family: 'Times New Roman', serif;
          font-size: 12pt;
          line-height: 1.5;
          color: black;
          background: white;
        }

        * {
          -webkit-print-color-adjust: exact !important;
          color-adjust: exact !important;
        }
      }

      body {
        font-family: 'Times New Roman', serif;
        font-size: 12pt;
        line-height: 1.5;
        color: black;
        background: white;
        max-width: ${pageStyles.width};
        margin: 0 auto;
        padding: ${pageStyles.margin};
      }

      p {
        margin: 0 0 10pt 0;
      }

      .center {
        text-align: center;
      }

      .justify {
        text-align: justify;
      }

      .bold {
        font-weight: bold;
      }

      .underline {
        text-decoration: underline;
      }
    </style>
  `;
}
