import { NextRequest, NextResponse } from 'next/server';
import { loadTemplates, loadTemplateFile, replacePlaceholders } from '@/lib/templates';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { templateId, data } = body;
    
    if (!templateId || !data) {
      return NextResponse.json(
        { error: 'Template ID and data are required' },
        { status: 400 }
      );
    }

    // Load templates to find the one to generate
    const templates = await loadTemplates();
    const template = templates.find(t => t.id === templateId);
    
    if (!template) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    // Load the template HTML content
    const templateContent = await loadTemplateFile(template.filename);
    
    // Replace placeholders with actual data
    const processedContent = replacePlaceholders(templateContent, data);
    
    // Return the processed HTML content
    return NextResponse.json({
      success: true,
      htmlContent: processedContent,
      templateName: template.name
    });
  } catch (error) {
    console.error('Error generating document:', error);
    return NextResponse.json(
      { error: 'Failed to generate document' },
      { status: 500 }
    );
  }
}
